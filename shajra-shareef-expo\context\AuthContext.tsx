import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { View, ActivityIndicator, StyleSheet, Platform } from 'react-native';
import auth, { FirebaseAuthTypes } from '@react-native-firebase/auth';
import { GoogleSignin, statusCodes } from '@react-native-google-signin/google-signin';
import {FIREBASE_GOOGLE_AUTH_WEB_CLIENT_ID} from '@env';
// Interface for Firebase User and Auth Error
type FirebaseUser = FirebaseAuthTypes.User;
type AuthError = FirebaseAuthTypes.NativeFirebaseAuthError; // Or a more specific error type if available/needed

interface AuthContextType {
  user: FirebaseUser | null;
  loading: boolean;
  isAuthReady: boolean; // To track if initial auth state check is complete
  signUpWithEmail: (email: string, pass: string) => Promise<FirebaseUser | AuthError>;
  signInWithEmail: (email: string, pass: string) => Promise<FirebaseUser | AuthError>;
  signInWithGoogle: () => Promise<FirebaseUser | AuthError>;
  signOutUser: () => Promise<void>;
  sendPasswordReset: (email: string) => Promise<void | AuthError>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// IMPORTANT: Configure Google Sign-In
// You should call this in your app's entry point or before any sign-in attempt.
// Replace 'YOUR_WEB_CLIENT_ID' with your actual Web Client ID from Firebase console
// (even for mobile, it's often the Web client ID that's used).
GoogleSignin.configure({
  webClientId: FIREBASE_GOOGLE_AUTH_WEB_CLIENT_ID, // Get this from your Firebase project settings -> Authentication -> Sign-in method -> Google
  offlineAccess: true, // if you want to access Google API on behalf of the user FROM YOUR SERVER
});

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [user, setUser] = useState<FirebaseUser | null>(null);
  const [loading, setLoading] = useState(true); // For individual operations
  const [isAuthReady, setIsAuthReady] = useState(false); // For initial onAuthStateChanged

  useEffect(() => {
    // onAuthStateChanged returns an unsubscriber
    const subscriber = auth().onAuthStateChanged(currentUser => {
      setUser(currentUser);
      if (!isAuthReady) {
        setIsAuthReady(true); // Initial auth state check is complete
      }
      setLoading(false); // Stop loading once user state is determined initially
    });
    return subscriber; // unsubscribe on unmount
  }, [isAuthReady]);


  const signUpWithEmail = async (email: string, pass: string): Promise<FirebaseUser | AuthError> => {
    setLoading(true);
    try {
      const userCredential = await auth().createUserWithEmailAndPassword(email, pass);
      // setUser(userCredential.user); // Handled by onAuthStateChanged
      return userCredential.user;
    } catch (error) {
      const authError = error as AuthError;
      console.error("Error signing up:", authError.code, authError.message);
      return authError;
    } finally {
      setLoading(false);
    }
  };

  const signInWithEmail = async (email: string, pass: string): Promise<FirebaseUser | AuthError> => {
    setLoading(true);
    try {
      const userCredential = await auth().signInWithEmailAndPassword(email, pass);
      // setUser(userCredential.user); // Handled by onAuthStateChanged
      return userCredential.user;
    } catch (error) {
      const authError = error as AuthError;
      console.error("Error signing in:", authError.code, authError.message);
      return authError;
    } finally {
      setLoading(false);
    }
  };

  const signInWithGoogle = async (): Promise<FirebaseUser | AuthError> => {
    setLoading(true);
    try {
      // Check if your device supports Google Play
      await GoogleSignin.hasPlayServices({ showPlayServicesUpdateDialog: true });
      // Get the users ID token
      const { idToken } = await GoogleSignin.signIn();

      // Create a Google credential with the token
      const googleCredential = auth.GoogleAuthProvider.credential(idToken);

      // Sign-in the user with the credential
      const userCredential = await auth().signInWithCredential(googleCredential);
      // setUser(userCredential.user); // Handled by onAuthStateChanged
      return userCredential.user;
    } catch (error: any) {
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        console.log("Google Sign-In cancelled by user");
      } else if (error.code === statusCodes.IN_PROGRESS) {
        console.log("Google Sign-In already in progress");
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        console.log("Google Play services not available or outdated");
      } else {
        console.error("Error signing in with Google:", error);
      }
      return error as AuthError; // Or a custom error object
    } finally {
      setLoading(false);
    }
  };

  const signOutUser = async () => {
    setLoading(true);
    try {
      // It's good practice to also sign out from Google if signed in with Google
      const isSignedInWithGoogle = await GoogleSignin.isSignedIn();
      if (isSignedInWithGoogle) {
        await GoogleSignin.revokeAccess(); // Optional: if you want to revoke access
        await GoogleSignin.signOut();
      }
      await auth().signOut();
      // setUser(null); // Handled by onAuthStateChanged
      // Navigation to login screen would typically be handled by a navigator
      // reacting to the user state change, e.g., in your App.tsx or root navigator.
      // Example: if (!user) { navigation.navigate('Login'); }
    } catch (error) {
      const authError = error as AuthError;
      console.error("Error signing out:", authError.code, authError.message);
    } finally {
      setLoading(false);
    }
  };

  const sendPasswordReset = async (email: string): Promise<void | AuthError> => {
    setLoading(true);
    try {
      await auth().sendPasswordResetEmail(email);
      // Optionally, provide user feedback (e.g., toast message)
      console.log("Password reset email sent to:", email);
    } catch (error) {
      const authError = error as AuthError;
      console.error("Error sending password reset email:", authError.code, authError.message);
      return authError;
    } finally {
      setLoading(false);
    }
  };

  // Show a loading indicator while the initial auth state is being determined
  // and isAuthReady is false.
  // Once isAuthReady is true, children will be rendered, and they can decide
  // what to show based on `user` and `loading` (for specific operations).
  if (!isAuthReady) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0000ff" />
      </View>
    );
  }

  return (
    <AuthContext.Provider value={{ user, loading, isAuthReady, signUpWithEmail, signInWithEmail, signInWithGoogle, signOutUser, sendPasswordReset }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5', // Or your app's background color
  },
});