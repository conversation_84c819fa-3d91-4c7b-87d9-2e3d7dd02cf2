import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { Platform } from 'react-native'; // For potential platform-specific logic if needed

export type FontSize = 'sm' | 'base' | 'lg';

// Define actual font size values for React Native
export const FONT_SIZE_VALUES: Record<FontSize, number> = {
  sm: 12,
  base: 16,
  lg: 20,
};

interface FontSizeContextType {
  fontSizeKey: FontSize; // e.g., 'sm', 'base', 'lg'
  fontSizeValue: number; // e.g., 12, 16, 20
  setFontSize: (fontSizeKey: FontSize) => void;
  isLoading: boolean; // To indicate if font size is being loaded
}

const FONT_SIZE_STORAGE_KEY = 'sabiriya-app-font-size';
const DEFAULT_FONT_SIZE_KEY: FontSize = 'base';

const FontSizeContext = createContext<FontSizeContextType | undefined>(undefined);

export const FontSizeProvider = ({ children }: { children: ReactNode }) => {
  const [fontSizeKey, _setFontSizeKey] = useState<FontSize>(DEFAULT_FONT_SIZE_KEY);
  const [isLoading, setIsLoading] = useState(true);

  // Load stored font size on initial mount
  useEffect(() => {
    const loadFontSize = async () => {
      try {
        const storedFontSize = await AsyncStorage.getItem(FONT_SIZE_STORAGE_KEY) as FontSize | null;
        if (storedFontSize && ['sm', 'base', 'lg'].includes(storedFontSize)) {
          _setFontSizeKey(storedFontSize);
        } else {
          // If nothing stored or invalid, set default and store it
          _setFontSizeKey(DEFAULT_FONT_SIZE_KEY);
          await AsyncStorage.setItem(FONT_SIZE_STORAGE_KEY, DEFAULT_FONT_SIZE_KEY);
        }
      } catch (error) {
        console.error('Failed to load font size from AsyncStorage:', error);
        // Fallback to default if loading fails
        _setFontSizeKey(DEFAULT_FONT_SIZE_KEY);
      } finally {
        setIsLoading(false);
      }
    };

    loadFontSize();
  }, []);

  const setFontSize = useCallback(async (newSizeKey: FontSize) => {
    _setFontSizeKey(newSizeKey);
    try {
      await AsyncStorage.setItem(FONT_SIZE_STORAGE_KEY, newSizeKey);
    } catch (error) {
      console.error('Failed to save font size to AsyncStorage:', error);
    }
  }, []);

  // Prevent rendering children until font size is loaded to avoid UI flicker
  // You might want to return a loading spinner or splash screen component here
  if (isLoading) {
    return null; // Or <AppLoadingScreen />
  }

  const contextValue: FontSizeContextType = {
    fontSizeKey,
    fontSizeValue: FONT_SIZE_VALUES[fontSizeKey],
    setFontSize,
    isLoading, // Consumers might want to know this, though usually handled by the provider
  };

  return (
    <FontSizeContext.Provider value={contextValue}>
      {children}
    </FontSizeContext.Provider>
  );
};

export const useFontSize = (): FontSizeContextType => {
  const context = useContext(FontSizeContext);
  if (context === undefined) {
    throw new Error('useFontSize must be used within a FontSizeProvider');
  }
  return context;
};