{"name": "eslint-import-resolver-babel-module", "version": "5.3.2", "main": "lib/index.js", "description": "babel-plugin-module-resolver resolver for eslint-plugin-import", "repository": {"type": "git", "url": "https://github.com/tleunen/eslint-import-resolver-babel-module.git"}, "engines": {"node": ">=10.0.0"}, "files": ["lib"], "author": "<PERSON> <<EMAIL>> (https://tommyleunen.com)", "license": "MIT", "keywords": ["eslint", "eslint-plugin-import", "eslint-import-resolver", "babel", "babel-plugin", "module", "resolver", "alias", "rewrite", "resolve", "rename", "mapping", "require", "import"], "dependencies": {"pkg-up": "^3.1.0", "resolve": "^1.20.0"}, "devDependencies": {"@babel/cli": "^7.13.14", "@babel/core": "^7.13.15", "@babel/preset-env": "^7.13.15", "babel-core": "^7.0.0-0", "babel-jest": "^26.6.3", "babel-plugin-module-resolver": "^5.0.0", "eslint": "^7.24.0", "jest": "^26.6.3", "lodash": "^4.17.21", "standard-version": "^9.2.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0", "babel-plugin-module-resolver": "^3.0.0 || ^4.0.0 || ^5.0.0"}, "scripts": {"lint": "eslint src test", "compile": "babel src --out-dir lib", "pretest": "npm run lint", "test": "jest", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "prepare": "npm run compile", "release": "standard-version"}, "jest": {"testEnvironment": "node", "testRegex": "/test/.*\\.test\\.js$", "collectCoverageFrom": ["src/**/*.js"]}, "greenkeeper": {"ignore": ["babel-jest", "eslint", "eslint-plugin-import"]}}