import React, { createContext, useContext, useEffect, useState, useCallback, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export type Language = 'en' | 'ar' | 'ur' | 'hi' | 'ro'; // Added hi and ro for future use

interface LanguageContextType {
  language: Language;
  setLanguage: (language: Language) => void;
  availableLanguages: { code: Language; name: string; disabled?: boolean }[];
  isLoadingLanguage: boolean; // To indicate if the language is being loaded from storage
}

const LANGUAGE_STORAGE_KEY = 'sabiriya-app-language';

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

const defaultLanguage: Language = 'en';

export const availableLanguagesList: LanguageContextType['availableLanguages'] = [
  { code: 'en', name: 'English' },
  { code: 'ar', name: 'العربية (Arabic)' },
  { code: 'ur', name: 'اردو (Urdu)' },
  { code: 'ro', name: 'Roman Urdu' },
  { code: 'hi', name: 'हिन्दी (Hindi)' }, // Enabled Hindi
];

export const LanguageProvider = ({ children }: { children: ReactNode }) => {
  const [language, _setLanguage] = useState<Language>(defaultLanguage);
  const [isLoadingLanguage, setIsLoadingLanguage] = useState<boolean>(true);

  // Effect to load language from AsyncStorage on component mount
  useEffect(() => {
    const loadLanguageFromStorage = async () => {
      try {
        const storedLanguage = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY) as Language | null;
        if (storedLanguage && availableLanguagesList.some(lang => lang.code === storedLanguage && !lang.disabled)) {
          _setLanguage(storedLanguage);
        } else {
          // If nothing is stored or stored language is invalid, set default and store it
          await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, defaultLanguage);
          _setLanguage(defaultLanguage);
        }
      } catch (error) {
        console.error('Failed to load language from AsyncStorage:', error);
        // Fallback to default language in case of error
        _setLanguage(defaultLanguage);
        try {
            await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, defaultLanguage);
        } catch (setError) {
            console.error('Failed to set default language to AsyncStorage after error:', setError);
        }
      } finally {
        setIsLoadingLanguage(false);
      }
    };

    loadLanguageFromStorage();
  }, []); // Empty dependency array ensures this runs only once on mount

const setLanguage = useCallback(async (lang: Language) => {
    if (availableLanguagesList.some(l => l.code === lang && !l.disabled)) {
      try {
        await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, lang);
        _setLanguage(lang);
      } catch (error) {
        console.error('Failed to save language to AsyncStorage:', error);
      }
    } else {
      console.warn(`Attempted to set an unavailable or disabled language: ${lang}`);
    }
  }, []);

  return (
    <LanguageContext.Provider value={{ language, setLanguage, availableLanguages: availableLanguagesList, isLoadingLanguage }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = (): LanguageContextType => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};