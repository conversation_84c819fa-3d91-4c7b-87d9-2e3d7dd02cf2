// firebase.ts or a similar file in your project

// Import the functions you need from the SDKs you need
import { initializeApp, getApp, getApps, type FirebaseApp } from "firebase/app";
import { getFirestore, type Firestore } from "firebase/firestore";
import { getStorage, type FirebaseStorage } from "firebase/storage";
// Use initializeAuth and getReactNativePersistence for React Native
import { initializeAuth, getReactNativePersistence, type Auth } from "firebase/auth/react-native";
import { getMessaging, type Messaging } from "firebase/messaging"; // For FCM
import AsyncStorage from '@react-native-async-storage/async-storage'; // For Auth persistence
//import Constants from 'expo-constants'; // For accessing app configuration
//import 'dotenv/config'; //For accessing environment variables
import {
FIREBASE_API_KEY,
FIREBASE_AUTH_DOMAIN,
FIREBASE_PROJECT_ID,
FIREBASE_STORAGE_BUCKET,
FIREBASE_MESSAGING_SENDER_ID,
FIREBASE_APP_ID,
FIREBASE_MEASUREMENT_ID } from '@env'
// const firebaseConfig = Constants.expoConfig?.extra?.firebase as {
//   apiKey: string;
//   authDomain: string;
//   projectId: string;
//   storageBucket: string;
//   messagingSenderId: string;
//   appId: string;
//   measurementId?: string; // Optional
// } | undefined;

const firebaseConfig = {
  apiKey: FIREBASE_API_KEY,
  authDomain: FIREBASE_AUTH_DOMAIN,
  projectId: FIREBASE_PROJECT_ID,
  storageBucket: FIREBASE_STORAGE_BUCKET,
  messagingSenderId: FIREBASE_MESSAGING_SENDER_ID,
  appId: FIREBASE_APP_ID,
  measurementId: FIREBASE_MEASUREMENT_ID,
};

// Initialize Firebase
let app: FirebaseApp | null = null;
let db: Firestore | null = null;
let storage: FirebaseStorage | null = null;
let auth: Auth | null = null;
let messaging: Messaging | null = null;

// Ensure Firebase is initialized only if config is valid
if (firebaseConfig && firebaseConfig.apiKey && firebaseConfig.projectId) {
  if (!getApps().length) {
    try {
      app = initializeApp(firebaseConfig);
    } catch (e) {
      console.error("Firebase app initialization failed:", e);
      // app remains null
    }
  } else {
    app = getApp();
  }

  if (app) {
    try {
      // Initialize Auth with React Native persistence
      auth = initializeAuth(app, {
        persistence: getReactNativePersistence(AsyncStorage)
      });
      db = getFirestore(app);
      storage = getStorage(app);
      // Only initialize messaging if sender ID is present to avoid errors if not configured
      if (firebaseConfig.messagingSenderId) {
        messaging = getMessaging(app);
      }
    } catch (e) {
      console.error("Error initializing Firebase services (auth, db, storage, messaging):", e);
      // services (auth, db, etc.) might remain null
    }
  } else {
    console.error(
      "Firebase app could not be initialized. This usually means the Firebase config is missing or incomplete from app.json/app.config.js. " +
      "Please ensure the 'extra.firebase' object is correctly set in your app configuration file, " +
      "and that you have rebuilt/restarted your development server/app."
    );
  }
} else {
  console.error(
    "Firebase Error: Critical Firebase configuration (apiKey or projectId) is missing or undefined from app.json/app.config.js. " +
    "Firebase will not be initialized. " +
    "Please ensure the 'extra.firebase' object and its properties are correctly set in your app configuration file " +
    "and you have rebuilt/restarted your development server/app."
  );
}

export { app, db, storage, auth, messaging };